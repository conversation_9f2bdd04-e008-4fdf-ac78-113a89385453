"use client"

import { Bad<PERSON> } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Eye,
  Download,
  ChevronLeft,
  ChevronRight,
  Truck,
  Package,
  CheckCircle,
  Clock,
  Search,
  Filter,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useState } from "react";
import OrderDetails from "./OrderDetails";

const orders = [
  {
    id: "ORD-2024-001",
    date: "2024-03-15",
    total: 2499.99,
    status: "Delivered",
    items: [
      {
        name: "M Sport Brake Kit",
        quantity: 1,
        price: 2499.99,
        oeCode: "34112450562",
        image:
          "https://images.unsplash.com/photo-1615906655593-ad0386982a0f?w=400&h=300&fit=crop",
      },
    ],
    billing: {
      name: "<PERSON>",
      address: "123 Main St",
      city: "San Francisco",
      state: "CA",
      zip: "94105",
      paymentMethod: "Visa",
      cardLast4: "4242",
    },
    shipping: {
      name: "John Doe",
      address: "123 Main St",
      city: "San Francisco",
      state: "CA",
      zip: "94105",
      method: "FedEx Ground",
      tracking: "1234567890",
      estimatedDelivery: "2024-03-18",
      currentLocation: "San Francisco, CA Distribution Center",
      shippingHistory: [
        {
          date: "2024-03-17T14:30:00Z",
          status: "Out for Delivery",
          location: "San Francisco, CA",
          description: "Package is out for delivery and will arrive today",
        },
        {
          date: "2024-03-17T08:15:00Z",
          status: "Arrived at Facility",
          location: "San Francisco, CA Distribution Center",
          description: "Package has arrived at the local distribution center",
        },
        {
          date: "2024-03-16T22:45:00Z",
          status: "In Transit",
          location: "Oakland, CA",
          description: "Package is in transit to the next facility",
        },
        {
          date: "2024-03-15T16:20:00Z",
          status: "Shipped",
          location: "Los Angeles, CA",
          description: "Package has been shipped from our warehouse",
        },
      ],
    },
  },
  {
    id: "ORD-2024-002",
    date: "2024-03-10",
    total: 3699.98,
    status: "Processing",
    items: [
      {
        name: "KW V3 Coilover Kit",
        quantity: 1,
        price: 2199.99,
        oeCode: "31302450678",
        image:
          "https://images.unsplash.com/photo-1619405399517-d7fce0f13302?w=400&h=300&fit=crop",
      },
      {
        name: "M Performance Air Intake",
        quantity: 1,
        price: 1499.99,
        oeCode: "13717450509",
        image:
          "https://images.unsplash.com/photo-1617814076367-b759c7d7e738?w=400&h=300&fit=crop",
      },
    ],
    billing: {
      name: "John Doe",
      address: "123 Main St",
      city: "San Francisco",
      state: "CA",
      zip: "94105",
      paymentMethod: "Mastercard",
      cardLast4: "8888",
    },
    shipping: {
      name: "John Doe",
      address: "123 Main St",
      city: "San Francisco",
      state: "CA",
      zip: "94105",
      method: "UPS Next Day Air",
      tracking: "1Z999AA1234567890",
      estimatedDelivery: "2024-03-12",
      currentLocation: "Processing at warehouse",
      shippingHistory: [
        {
          date: "2024-03-10T10:30:00Z",
          status: "Order Processed",
          location: "Los Angeles, CA Warehouse",
          description:
            "Your order has been processed and is being prepared for shipment",
        },
      ],
    },
  },
];

const OrdersPage = () => {
  const [selectedOrder, setSelectedOrder] = useState<(typeof orders)[0] | null>(
    null,
  );
  const [showShippingOnly, setShowShippingOnly] = useState(false);
  const [orderType, setOrderType] = useState<string>("all");
  const [searchQuery, setSearchQuery] = useState<string>("");

  const getShippingStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "delivered":
        return <CheckCircle className="w-4 h-4" />;
      case "in transit":
      case "shipped":
        return <Truck className="w-4 h-4" />;
      case "processing":
        return <Package className="w-4 h-4" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const getShippingStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "delivered":
        return "text-green-600 hover:text-green-700";
      case "in transit":
      case "shipped":
        return "text-blue-600 hover:text-blue-700";
      case "processing":
        return "text-orange-600 hover:text-orange-700";
      default:
        return "text-gray-600 hover:text-gray-700";
    }
  };

  const handleShippingStatus = (order: (typeof orders)[0]) => {
    setSelectedOrder(order);
    setShowShippingOnly(true);
  };

  const handleViewDetails = (order: (typeof orders)[0]) => {
    setSelectedOrder(order);
    setShowShippingOnly(false);
  };
  return (
    <div className="max-w-[1640px] mx-auto p-4 sm:p-6 lg:p-8 flex-col items-center justify-center rounded-lg text-center">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">My Orders</h1>
      </div>

      {/* Filters and Search */}
      <div className="flex flex-col sm:flex-row gap-4 mb-6">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search orders by ID, product name, or OE code..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2">
          <Select value={orderType} onValueChange={setOrderType}>
            <SelectTrigger className="w-48">
              <Filter className="w-4 h-4 mr-2" />
              <SelectValue placeholder="Order Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Orders</SelectItem>
              <SelectItem value="delivered">Delivered</SelectItem>
              <SelectItem value="processing">Processing</SelectItem>
              <SelectItem value="shipped">Shipped</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-4">
        {orders.map((order) => (
          <div
            key={order.id}
            className="border border-gray-200 rounded-lg p-4 hover:border-[#0066B1] transition-colors"
          >
            <div className="flex flex-wrap gap-4 justify-between items-start mb-3">
              <div>
                <h3 className="font-medium text-base">{order.id}</h3>
                <p className="text-xs text-gray-500">
                  {new Date(order.date).toLocaleDateString()}
                </p>
              </div>
              <Badge
                className={cn(
                  order.status === "Delivered"
                    ? "bg-green-100 text-green-800"
                    : "bg-blue-100 text-blue-800",
                )}
              >
                {order.status}
              </Badge>
            </div>

            <div className="space-y-2 mb-3">
              {order.items.slice(0, 2).map((item, index) => (
                <div key={index} className="flex justify-between items-center">
                  <div>
                    <p className="font-medium text-sm">{item.name}</p>
                    <p className="text-xs text-gray-500">
                      Qty: {item.quantity} • ${item.price.toLocaleString()}
                    </p>
                  </div>
                </div>
              ))}
              {order.items.length > 2 && (
                <p className="text-xs text-gray-500">
                  +{order.items.length - 2} more items
                </p>
              )}
            </div>

            <div className="pt-3 border-t border-gray-100 flex flex-wrap items-center justify-between gap-2">
              <div className="font-semibold text-base">
                ${order.total.toLocaleString()}
              </div>
              <div className="flex gap-1">
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-1 text-xs px-2 py-1 h-7"
                  onClick={() => handleViewDetails(order)}
                >
                  <Eye className="w-3 h-3" /> Details
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-1 text-xs px-2 py-1 h-7"
                >
                  <Download className="w-3 h-3" /> Invoice
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  className={`gap-1 text-xs px-2 py-1 h-7 ${getShippingStatusColor(order.status)}`}
                  onClick={() => handleShippingStatus(order)}
                  title={`Shipping Status: ${order.status}`}
                >
                  {getShippingStatusIcon(order.status)} Track
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {selectedOrder && (
        <OrderDetails
          order={selectedOrder}
          open={!!selectedOrder}
          onOpenChange={(open) => !open && setSelectedOrder(null)}
          showShippingOnly={showShippingOnly}
        />
      )}

      {/* Pagination */}
      <div className="mt-8 flex justify-center">
        <nav className="flex items-center space-x-2">
          <Button variant="outline" size="sm" disabled>
            <ChevronLeft className="h-4 w-4 mr-1" /> Previous
          </Button>
          <Button
            variant="outline"
            size="sm"
            className={cn(
              "min-w-[2.5rem]",
              "bg-[#0066B1] text-white hover:bg-[#004d85]",
            )}
          >
            1
          </Button>
          <Button variant="outline" size="sm" className="min-w-[2.5rem]">
            2
          </Button>
          <Button variant="outline" size="sm" className="min-w-[2.5rem]">
            3
          </Button>
          <Button variant="outline" size="sm">
            Next <ChevronRight className="h-4 w-4 ml-1" />
          </Button>
        </nav>
      </div>
    </div>
  );
};

export default OrdersPage;
