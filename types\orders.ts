import type { 
  OrderStatus as PrismaOrderStatus,
  PaymentStatus as PrismaPaymentStatus,
  PaymentMethod as PrismaPaymentMethod,
  ShippingMethod as PrismaShippingMethod,
  ShipmentStatus as PrismaShipmentStatus,
  Showroom as PrismaShowroom,
  DiscountType as PrismaDiscountType
} from "@/generated/prisma";

// Order Product Interface for UI
export interface OrderProduct {
  id: string;
  name: string;
  oeCode: string;
  image: string;
  quantity: number;
  price: number;
  hasDiscount: boolean;
  discountPercentage: number | null;
  activeDiscountType: PrismaDiscountType | null;
  activeDiscountValue: number | null;
}

// Billing Address Interface
export interface OrderBillingAddress {
  fullName: string;
  companyName?: string;
  address: string;
  city: string;
  county: string;
  cui?: string;
  bank?: string;
  iban?: string;
}

// Shipping Address Interface
export interface OrderShippingAddress {
  fullName: string;
  address: string;
  city: string;
  county: string;
  phoneNumber: string;
  notes?: string;
}

// Shipping History Interface
export interface ShippingHistoryEvent {
  date: string;
  status: string;
  location: string;
  description: string;
}

// Order Interface for UI
export interface Order {
  id: string;
  orderNumber: string;
  date: string;
  total: number;
  status: string;
  orderStatus: PrismaOrderStatus;
  paymentStatus: PrismaPaymentStatus;
  paymentMethod: PrismaPaymentMethod;
  shippingMethod: PrismaShippingMethod;
  shipmentStatus: PrismaShipmentStatus;
  showroom?: PrismaShowroom;
  isPaid: boolean;
  items: OrderProduct[];
  billing?: OrderBillingAddress;
  shipping?: OrderShippingAddress;
  tracking?: string;
  estimatedDelivery?: string;
  currentLocation?: string;
  shippingHistory?: ShippingHistoryEvent[];
  notes?: string;
  vin?: string;
  invoiceAM?: string;
  // Timestamps
  placedAt: string;
  processedAt?: string;
  completedAt?: string;
  cancelledAt?: string;
  shippingProcessedAt?: string;
  shippedAt?: string;
  deliveredAt?: string;
  paidAt?: string;
  refundedAt?: string;
}

// Raw Order Item from Prisma (for data fetching)
export interface RawOrderItemFromPrisma {
  id: string;
  quantity: number;
  price: number;
  notes?: string;
  vinOrderItem?: string;
  product: {
    id: string;
    Material_Number: string;
    Description_Local: string | null;
    PretAM: number | null;
    FinalPrice: number | null;
    ImageUrl: string[];
    HasDiscount: boolean;
    discountPercentage: number | null;
    activeDiscountType: PrismaDiscountType | null;
    activeDiscountValue: number | null;
  };
}

// Raw Order from Prisma (for data fetching)
export interface RawOrderFromPrisma {
  id: string;
  orderNumber: string;
  amount: number;
  isPaid: boolean;
  vin?: string;
  invoiceAM?: string;
  notes?: string;
  orderStatus: PrismaOrderStatus;
  paymentStatus: PrismaPaymentStatus;
  paymentMethod: PrismaPaymentMethod;
  shippingMethod: PrismaShippingMethod;
  shipmentStatus: PrismaShipmentStatus;
  showroom?: PrismaShowroom;
  placedAt: Date;
  processedAt?: Date;
  completedAt?: Date;
  cancelledAt?: Date;
  shippingProcessedAt?: Date;
  shippedAt?: Date;
  deliveredAt?: Date;
  paidAt?: Date;
  refundedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
  orderItems: RawOrderItemFromPrisma[];
  billingAddress?: {
    fullName: string;
    companyName?: string;
    address: string;
    city: string;
    county: string;
    cui?: string;
    bank?: string;
    iban?: string;
  };
  shippingAddress?: {
    fullName: string;
    address: string;
    city: string;
    county: string;
    phoneNumber: string;
    notes?: string;
  };
}

// Order List Response Interface
export interface OrdersResponse {
  orders: Order[];
  pagination: {
    total: number;
    pages: number;
    currentPage: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// Order Filters Interface
export interface OrderFilters {
  status?: string;
  search?: string;
  page?: number;
  limit?: number;
  dateFrom?: string;
  dateTo?: string;
}

// Order Status Mapping
export const ORDER_STATUS_LABELS: Record<PrismaOrderStatus, string> = {
  plasata: "Placed",
  procesare: "Processing", 
  confirmata: "Confirmed",
  pregatita: "Prepared",
  expediata: "Shipped",
  livrata: "Delivered",
  completa: "Complete",
  anulata: "Cancelled",
  stornata: "Voided",
  returnata: "Returned",
  partiala: "Partially Fulfilled"
};

// Payment Status Mapping
export const PAYMENT_STATUS_LABELS: Record<PrismaPaymentStatus, string> = {
  asteptare: "Pending",
  procesare: "Processing",
  platita: "Paid",
  esuata: "Failed",
  rambursata: "Refunded",
  anulata: "Cancelled"
};

// Shipment Status Mapping
export const SHIPMENT_STATUS_LABELS: Record<PrismaShipmentStatus, string> = {
  asteptare: "Pending",
  procesare: "Processing",
  expediata: "Shipped",
  intransit: "In Transit",
  livrata: "Delivered",
  returnata: "Returned",
  anulata: "Cancelled"
};

// Payment Method Mapping
export const PAYMENT_METHOD_LABELS: Record<PrismaPaymentMethod, string> = {
  ramburs: "Cash on Delivery",
  card: "Credit Card",
  transfer: "Bank Transfer",
  paypal: "PayPal",
  crypto: "Cryptocurrency"
};

// Shipping Method Mapping
export const SHIPPING_METHOD_LABELS: Record<PrismaShippingMethod, string> = {
  curier: "Courier",
  showroom: "Pickup from Showroom"
};
