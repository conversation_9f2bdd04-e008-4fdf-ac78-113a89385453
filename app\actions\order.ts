"use server"

import { getCurrentDbUser } from "@/lib/auth";
import { prisma, withRetry } from "@/lib/db";
import { logger } from "@/lib/logger";
import { redis } from "@/lib/redis";
import { Cart } from "@/types/cart";
import { revalidatePath } from "next/cache";

export async function placeOrder() {
  try {
    const user = await getCurrentDbUser();
    if (!user) {
      logger.error(`[placeOrder] No user authenticated`);
      return { success: false };
    }

    if (!redis) {
      logger.error("[placeOrder] Redis connection not available");
      return { success: false };
    }

    // Retrieve the cart from Redis
    let cart = await redis.get<Cart>(`cart-${user.id}`);

    if (!cart) {
      logger.error(`[placeOrder] No cart found for user ${user.id}`);
      return { success: false };
    }

    if (!cart.items.length) {
      logger.error(`[placeOrder] No items in cart for user ${user.id}`);
      return { success: false };
    }

    // Create the order
    const order = await withRetry(() =>
      prisma.order.create({
        data: {
          userId: user.id,
          amount: cart.items.reduce((acc, item) => acc + (item.FinalPrice ? item.FinalPrice : 0) * item.quantity, 0),
          orderNumber: `ORD-${new Date().getFullYear()}-${Math.floor(Math.random() * 100000)}`,
          notes: cart.order?.notes,
          createdBy: user.id,
        },
      })
    );

    // Create the order items
    await withRetry(() =>
      prisma.orderItem.createMany({
        data: cart.items.map((item) => ({
          orderId: order.id,
          productId: item.id,
          quantity: item.quantity,
          price: item.FinalPrice ?? 0,
          notes: item.vinNotes,
          notesToInvoice: item.addVinNotesToInvoice,
          vinOrderItem: item.vinNotes,
          createdBy: user.id,
        })),
      })
    );

    await redis.del(`cart-${user.id}`);
    await redis.del(`user-orders:${user.id}`);
    revalidatePath("/cart", "layout");
  
    return { success: true };
  } catch (error) {
    console.error("Error placing order:", error);
    return { success: false };
  }
}