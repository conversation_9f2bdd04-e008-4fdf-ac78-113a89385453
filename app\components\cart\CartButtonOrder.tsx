"use client"

import { placeOrder } from "@/app/actions/order";
import { But<PERSON> } from "@/components/ui/button";
import { Cart } from "@/types/cart";
import { Loader2 } from "lucide-react";
import { useTransition } from "react";
import { toast } from "sonner";

export function CartButtonOrder( { cart }: { cart: Cart }) {
    const [isPending, startTransition] = useTransition();

    const handleOrder = async () => {
        startTransition(async () => {
          try {
            const response = await placeOrder();
      
            if (response.success) {
              toast.success("Comanda a fost plasata cu succes.");
            } else {
              toast.error("Eșec la plasarea comenzii.");
            }
          } catch (err) {
            toast.error("Nu s-a putut plasa comanda.");
          }
        });
    }

  return (
    <>
        {isPending ? (
            <Button
              disabled
              className="w-full mt-6 bg-[#0066B1] hover:bg-[#004d85]"
            >
              <Loader2 className="w-4 h-4 animate-spin" /> 
            </Button>
          ) : (
            <Button
              onClick={handleOrder}
              className="w-full mt-6 bg-[#0066B1] hover:bg-[#004d85]"
            >
              Lanseaza comanda
            </Button>
          )}      
    </>
  );
}