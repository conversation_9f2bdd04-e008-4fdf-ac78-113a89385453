"server-only"

import { prisma, withRetry } from "@/lib/db"
import { logger } from "@/lib/logger"
import { getCachedData } from "@/lib/redis-cache"
import { toSafeNumber } from "@/lib/utils"
import { cuidSchema } from "@/lib/zod"
import {
  Order,
  OrdersResponse,
  OrderFilters,
  RawOrderFromPrisma,
  ORDER_STATUS_LABELS,
  PAYMENT_STATUS_LABELS,
  SHIPMENT_STATUS_LABELS,
  PAYMENT_METHOD_LABELS,
  SHIPPING_METHOD_LABELS
} from "@/types/orders"
import { cache } from "react"
import { z } from "zod"

// Validation schemas
const orderFiltersSchema = z.object({
  status: z.string().optional(),
  search: z.string().optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  dateFrom: z.string().optional(),
  dateTo: z.string().optional(),
});

/**
 * Transform raw order data from Prisma to UI-friendly format
 */
function transformOrderData(rawOrder: RawOrderFromPrisma): Order {
  return {
    id: rawOrder.id,
    orderNumber: rawOrder.orderNumber,
    date: rawOrder.createdAt.toISOString(),
    total: toSafeNumber(rawOrder.amount) || 0,
    status: ORDER_STATUS_LABELS[rawOrder.orderStatus] || rawOrder.orderStatus,
    orderStatus: rawOrder.orderStatus,
    paymentStatus: rawOrder.paymentStatus,
    paymentMethod: rawOrder.paymentMethod,
    shippingMethod: rawOrder.shippingMethod,
    shipmentStatus: rawOrder.shipmentStatus,
    showroom: rawOrder.showroom || undefined,
    isPaid: rawOrder.isPaid,
    items: rawOrder.orderItems.map(item => ({
      id: item.id,
      name: item.product.Description_Local || item.product.Material_Number,
      oeCode: item.product.Material_Number,
      image: item.product.ImageUrl?.[0] || "/placeholder-product.jpg",
      quantity: item.quantity,
      price: toSafeNumber(item.price) || 0,
      hasDiscount: item.product.HasDiscount,
      discountPercentage: toSafeNumber(item.product.discountPercentage),
      activeDiscountType: item.product.activeDiscountType,
      activeDiscountValue: toSafeNumber(item.product.activeDiscountValue),
    })),
    billing: rawOrder.billingAddress ? {
      fullName: rawOrder.billingAddress.fullName,
      companyName: rawOrder.billingAddress.companyName || undefined,
      address: rawOrder.billingAddress.address,
      city: rawOrder.billingAddress.city,
      county: rawOrder.billingAddress.county,
      cui: rawOrder.billingAddress.cui || undefined,
      bank: rawOrder.billingAddress.bank || undefined,
      iban: rawOrder.billingAddress.iban || undefined,
    } : undefined,
    shipping: rawOrder.shippingAddress ? {
      fullName: rawOrder.shippingAddress.fullName,
      address: rawOrder.shippingAddress.address,
      city: rawOrder.shippingAddress.city,
      county: rawOrder.shippingAddress.county,
      phoneNumber: rawOrder.shippingAddress.phoneNumber,
      notes: rawOrder.shippingAddress.notes || undefined,
    } : undefined,
    notes: rawOrder.notes || undefined,
    vin: rawOrder.vin || undefined,
    invoiceAM: rawOrder.invoiceAM || undefined,
    // Timestamps
    placedAt: rawOrder.placedAt.toISOString(),
    processedAt: rawOrder.processedAt?.toISOString(),
    completedAt: rawOrder.completedAt?.toISOString(),
    cancelledAt: rawOrder.cancelledAt?.toISOString(),
    shippingProcessedAt: rawOrder.shippingProcessedAt?.toISOString(),
    shippedAt: rawOrder.shippedAt?.toISOString(),
    deliveredAt: rawOrder.deliveredAt?.toISOString(),
    paidAt: rawOrder.paidAt?.toISOString(),
    refundedAt: rawOrder.refundedAt?.toISOString(),
  };
}

/**
 * Get orders for a specific user with filtering and pagination
 */
export const getUserOrders = cache(async (
  userId: string,
  filters: OrderFilters = {}
): Promise<OrdersResponse> => {
  console.log("getUserOrders called");

  if (!userId) {
    logger.warn(`[getUserOrders] No userId provided`);
    return { orders: [], pagination: { total: 0, pages: 0, currentPage: 1, hasNext: false, hasPrev: false } };
  }

  const userIdParsed = cuidSchema.safeParse(userId);
  if (!userIdParsed.success) {
    logger.error("[getUserOrders] Invalid user ID provided:", userIdParsed.error.format());
    return { orders: [], pagination: { total: 0, pages: 0, currentPage: 1, hasNext: false, hasPrev: false } };
  }

  const filtersParsed = orderFiltersSchema.safeParse(filters);
  if (!filtersParsed.success) {
    logger.error("[getUserOrders] Invalid filters provided:", filtersParsed.error.format());
    return { orders: [], pagination: { total: 0, pages: 0, currentPage: 1, hasNext: false, hasPrev: false } };
  }

  const validatedUserId = userIdParsed.data;
  const validatedFilters = filtersParsed.data;
  const { page, limit, status, search, dateFrom, dateTo } = validatedFilters;

  const skip = (page - 1) * limit;
  const cacheKey = `user-orders:${validatedUserId}:${JSON.stringify(validatedFilters)}`;

  try {
    return await getCachedData(
      cacheKey,
      async () => {
        // Build where clause
        const whereClause: any = {
          userId: validatedUserId,
          isActive: true,
        };

        // Add status filter
        if (status && status !== "all") {
          whereClause.orderStatus = status;
        }

        // Add search filter
        if (search) {
          whereClause.OR = [
            { orderNumber: { contains: search, mode: "insensitive" } },
            { notes: { contains: search, mode: "insensitive" } },
            {
              orderItems: {
                some: {
                  product: {
                    OR: [
                      { Material_Number: { contains: search, mode: "insensitive" } },
                      { Description_Local: { contains: search, mode: "insensitive" } }
                    ]
                  }
                }
              }
            }
          ];
        }

        // Add date filters
        if (dateFrom) {
          whereClause.createdAt = { ...whereClause.createdAt, gte: new Date(dateFrom) };
        }
        if (dateTo) {
          whereClause.createdAt = { ...whereClause.createdAt, lte: new Date(dateTo) };
        }

        // Fetch orders and total count in parallel
        const [rawOrders, totalCount] = await Promise.all([
          withRetry(() =>
            prisma.order.findMany({
              where: whereClause,
              orderBy: { createdAt: "desc" },
              skip,
              take: limit,
              select: {
                id: true,
                orderNumber: true,
                amount: true,
                isPaid: true,
                vin: true,
                invoiceAM: true,
                notes: true,
                orderStatus: true,
                paymentStatus: true,
                paymentMethod: true,
                shippingMethod: true,
                shipmentStatus: true,
                showroom: true,
                placedAt: true,
                processedAt: true,
                completedAt: true,
                cancelledAt: true,
                shippingProcessedAt: true,
                shippedAt: true,
                deliveredAt: true,
                paidAt: true,
                refundedAt: true,
                createdAt: true,
                updatedAt: true,
                orderItems: {
                  select: {
                    id: true,
                    quantity: true,
                    price: true,
                    notes: true,
                    vinOrderItem: true,
                    product: {
                      select: {
                        id: true,
                        Material_Number: true,
                        Description_Local: true,
                        PretAM: true,
                        FinalPrice: true,
                        ImageUrl: true,
                        HasDiscount: true,
                        discountPercentage: true,
                        activeDiscountType: true,
                        activeDiscountValue: true,
                      },
                    },
                  },
                },
                billingAddress: {
                  select: {
                    fullName: true,
                    companyName: true,
                    address: true,
                    city: true,
                    county: true,
                    cui: true,
                    bank: true,
                    iban: true,
                  },
                },
                shippingAddress: {
                  select: {
                    fullName: true,
                    address: true,
                    city: true,
                    county: true,
                    phoneNumber: true,
                    notes: true,
                  },
                },
              },
            })
          ) as Promise<RawOrderFromPrisma[]>,
          withRetry(() => prisma.order.count({ where: whereClause }))
        ]);

        // Transform orders
        const orders = rawOrders.map(transformOrderData);

        // Calculate pagination
        const totalPages = Math.ceil(totalCount / limit);
        const pagination = {
          total: totalCount,
          pages: totalPages,
          currentPage: page,
          hasNext: page < totalPages,
          hasPrev: page > 1,
        };

        return { orders, pagination };
      },
      60 * 2 // 2 minutes TTL for orders data
    );
  } catch (error) {
    logger.error(`[getUserOrders] Error fetching orders for user ${validatedUserId}:`, error);

    // Return empty result instead of throwing to prevent page crashes
    // The UI will handle the empty state gracefully
    return {
      orders: [],
      pagination: {
        total: 0,
        pages: 0,
        currentPage: validatedFilters.page,
        hasNext: false,
        hasPrev: false
      }
    };
  }
});

/**
 * Get a single order by ID for a specific user
 */
export const getUserOrderById = cache(async (
  userId: string,
  orderId: string
): Promise<Order | null> => {
  console.log("getUserOrderById called");

  if (!userId || !orderId) {
    logger.warn(`[getUserOrderById] Missing userId or orderId`);
    return null;
  }

  const userIdParsed = cuidSchema.safeParse(userId);
  const orderIdParsed = cuidSchema.safeParse(orderId);

  if (!userIdParsed.success || !orderIdParsed.success) {
    logger.error("[getUserOrderById] Invalid userId or orderId provided");
    return null;
  }

  const validatedUserId = userIdParsed.data;
  const validatedOrderId = orderIdParsed.data;
  const cacheKey = `user-order:${validatedUserId}:${validatedOrderId}`;

  try {
    return await getCachedData(
      cacheKey,
      async () => {
        const rawOrder = await withRetry(() =>
          prisma.order.findFirst({
            where: {
              id: validatedOrderId,
              userId: validatedUserId,
              isActive: true,
            },
            select: {
              id: true,
              orderNumber: true,
              amount: true,
              isPaid: true,
              vin: true,
              invoiceAM: true,
              notes: true,
              orderStatus: true,
              paymentStatus: true,
              paymentMethod: true,
              shippingMethod: true,
              shipmentStatus: true,
              showroom: true,
              placedAt: true,
              processedAt: true,
              completedAt: true,
              cancelledAt: true,
              shippingProcessedAt: true,
              shippedAt: true,
              deliveredAt: true,
              paidAt: true,
              refundedAt: true,
              createdAt: true,
              updatedAt: true,
              orderItems: {
                select: {
                  id: true,
                  quantity: true,
                  price: true,
                  notes: true,
                  vinOrderItem: true,
                  product: {
                    select: {
                      id: true,
                      Material_Number: true,
                      Description_Local: true,
                      PretAM: true,
                      FinalPrice: true,
                      ImageUrl: true,
                      HasDiscount: true,
                      discountPercentage: true,
                      activeDiscountType: true,
                      activeDiscountValue: true,
                    },
                  },
                },
              },
              billingAddress: {
                select: {
                  fullName: true,
                  companyName: true,
                  address: true,
                  city: true,
                  county: true,
                  cui: true,
                  bank: true,
                  iban: true,
                },
              },
              shippingAddress: {
                select: {
                  fullName: true,
                  address: true,
                  city: true,
                  county: true,
                  phoneNumber: true,
                  notes: true,
                },
              },
            },
          })
        ) as RawOrderFromPrisma | null;

        if (!rawOrder) {
          return null;
        }

        return transformOrderData(rawOrder);
      },
      60 * 5 // 5 minutes TTL for single order data
    );
  } catch (error) {
    logger.error(`[getUserOrderById] Error fetching order ${validatedOrderId} for user ${validatedUserId}:`, error);

    // Return null to indicate order not found/error
    // The UI should handle this gracefully
    return null;
  }
});