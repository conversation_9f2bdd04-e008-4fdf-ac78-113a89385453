import { getCurrentDbUser } from "@/lib/auth";
import CartPage from "../components/cart/CartPage";
import { Cart } from "@/types/cart";
import { getCart } from "../getData/cart";
import { ShoppingCart } from "lucide-react";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { redirect } from "next/navigation";

export default async function CartRoute() {
    const user = await getCurrentDbUser()

    if(!user) redirect("sign-in")

    const cart: Cart | null = await getCart(user.id)

    //const stoc = await getPretSiStocBatch(cartItems.map((item) => item.Material_Number))

    if (!cart || cart.items.length === 0 || !cart.items) return (
        <div className="flex max-w-[1640px] mx-auto p-4 sm:p-6 lg:p-8 flex-col items-center justify-center rounded-lg border border-dashed text-center">
            <div className="flex h-20 w-20 items-center justify-center rounded-full bg-primary/10">
                <ShoppingCart className="w-10 h-10 text-primary" />
            </div>
            <h2 className="mt-6 text-xl font-semibold">Cosul tau este gol</h2>
            <p className="mb-8 mt-2 text-center text-sm leading-6 text-muted-foreground max-w-sm mx-auto">
                Nu ai adaugat inca niciun produs in cos. Exploreaza produsele noastre si adauga-le in cos.
            </p>
            <Button asChild>
                <Link href="/">Vezi produsele</Link>
            </Button>
        </div>
    )

    return (
        <CartPage cart={cart} />
    )
}
