import OrdersPage from "@/app/components/account/orders/OrdersPage";
import { getCurrentDbUser } from "@/lib/auth";
import { getUserOrders } from "@/app/getData/orders";
import { redirect } from "next/navigation";
import { OrderFilters } from "@/types/orders";
import { Package } from "lucide-react";
import { logger } from "@/lib/logger";

interface OrdersRouteProps {
  searchParams: Promise<{
    page?: string;
    status?: string;
    search?: string;
    limit?: string;
  }>;
}

export default async function OrdersRoute({ searchParams }: OrdersRouteProps) {
  try {
    // Get current user
    const user = await getCurrentDbUser();

    if (!user) {
      redirect("/sign-in");
    }

    // Await search params (Next.js 15 requirement)
    const params = await searchParams;

    // Parse search params with validation
    const filters: OrderFilters = {
      page: params.page ? Math.max(1, parseInt(params.page) || 1) : 1,
      status: params.status || "all",
      search: params.search || "",
      limit: params.limit ? Math.min(50, Math.max(5, parseInt(params.limit) || 10)) : 10,
    };

    // Fetch orders data
    const ordersData = await getUserOrders(user.id, filters);

    return (
      <OrdersPage
        initialOrders={ordersData.orders}
        pagination={ordersData.pagination}
        filters={filters}
      />
    );
  } catch (error) {
    logger.error("[OrdersRoute] Error fetching orders:", error);

    // Return error fallback for server-side errors
    return (
      <div className="max-w-[1640px] mx-auto p-4 sm:p-6 lg:p-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-semibold">My Orders</h1>
        </div>

        <div className="text-center py-12">
          <Package className="mx-auto h-12 w-12 text-red-500 mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Unable to load orders
          </h2>
          <p className="text-gray-600 mb-6 max-w-md mx-auto">
            There was a problem loading your orders. Please refresh the page or try again later.
          </p>
        </div>
      </div>
    );
  }
};