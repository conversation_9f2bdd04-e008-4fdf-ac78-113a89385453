import OrdersPage from "@/app/components/account/orders/OrdersPage";
import OrdersErrorBoundary from "@/app/components/account/orders/OrdersErrorBoundary";
import { getCurrentDbUser } from "@/lib/auth";
import { getUserOrders } from "@/app/getData/orders";
import { redirect } from "next/navigation";
import { OrderFilters } from "@/types/orders";
import { Suspense } from "react";
import { Package } from "lucide-react";

interface OrdersRouteProps {
  searchParams: {
    page?: string;
    status?: string;
    search?: string;
    limit?: string;
  };
}

// Loading component
function OrdersLoading() {
  return (
    <div className="max-w-[1640px] mx-auto p-4 sm:p-6 lg:p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">My Orders</h1>
      </div>

      <div className="flex items-center justify-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#0066B1]"></div>
        <span className="ml-2 text-gray-600">Loading your orders...</span>
      </div>
    </div>
  );
}

// Error fallback component
function OrdersError({ error, retry }: { error?: Error; retry: () => void }) {
  return (
    <div className="max-w-[1640px] mx-auto p-4 sm:p-6 lg:p-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-semibold">My Orders</h1>
      </div>

      <div className="text-center py-12">
        <Package className="mx-auto h-12 w-12 text-red-500 mb-4" />
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          Failed to load orders
        </h2>
        <p className="text-gray-600 mb-6 max-w-md mx-auto">
          We couldn't load your orders right now. Please try again.
        </p>
        <button
          onClick={retry}
          className="px-4 py-2 bg-[#0066B1] text-white rounded-md hover:bg-[#004d85] transition-colors"
        >
          Try Again
        </button>
      </div>
    </div>
  );
}

export default async function OrdersRoute({ searchParams }: OrdersRouteProps) {
  try {
    // Get current user
    const user = await getCurrentDbUser();

    if (!user) {
      return redirect("/sign-in");
    }

    // Parse search params with validation
    const filters: OrderFilters = {
      page: searchParams.page ? Math.max(1, parseInt(searchParams.page) || 1) : 1,
      status: searchParams.status || "all",
      search: searchParams.search || "",
      limit: searchParams.limit ? Math.min(50, Math.max(5, parseInt(searchParams.limit) || 10)) : 10,
    };

    // Fetch orders data
    const ordersData = await getUserOrders(user.id, filters);

    return (
      <OrdersErrorBoundary fallback={OrdersError}>
        <Suspense fallback={<OrdersLoading />}>
          <OrdersPage
            initialOrders={ordersData.orders}
            pagination={ordersData.pagination}
            filters={filters}
          />
        </Suspense>
      </OrdersErrorBoundary>
    );
  } catch (error) {
    console.error("Error in OrdersRoute:", error);

    // Return error fallback for server-side errors
    return (
      <div className="max-w-[1640px] mx-auto p-4 sm:p-6 lg:p-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-semibold">My Orders</h1>
        </div>

        <div className="text-center py-12">
          <Package className="mx-auto h-12 w-12 text-red-500 mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Unable to load orders
          </h2>
          <p className="text-gray-600 mb-6 max-w-md mx-auto">
            There was a problem loading your orders. Please refresh the page or try again later.
          </p>
        </div>
      </div>
    );
  }
};